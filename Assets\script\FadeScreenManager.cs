using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;

/// <summary>
/// Manages a fade screen effect for smooth transitions
/// This script should be attached to a Canvas with a CanvasGroup component
/// </summary>
public class FadeScreenManager : MonoBehaviour
{
    [Header("Fade Screen Components")]
    public CanvasGroup canvasGroup;
    public Image fadeImage;
    
    [Head<PERSON>("Fade Settings")]
    public Color fadeColor = Color.black;
    public float defaultFadeDuration = 0.5f;
    
    private void Awake()
    {
        // Auto-setup if components are not assigned
        if (canvasGroup == null)
            canvasGroup = GetComponent<CanvasGroup>();
            
        if (fadeImage == null)
            fadeImage = GetComponentInChildren<Image>();
            
        // Initialize fade screen
        if (canvasGroup != null)
        {
            canvasGroup.alpha = 0f;
            canvasGroup.gameObject.SetActive(false);
        }
        
        if (fadeImage != null)
        {
            fadeImage.color = fadeColor;
        }
    }
    
    /// <summary>
    /// Fade to black (or specified color)
    /// </summary>
    public void FadeIn(float duration = -1f)
    {
        if (duration < 0) duration = defaultFadeDuration;
        
        if (canvasGroup != null)
        {
            canvasGroup.gameObject.SetActive(true);
            canvasGroup.DOFade(1f, duration);
        }
    }
    
    /// <summary>
    /// Fade to clear
    /// </summary>
    public void FadeOut(float duration = -1f)
    {
        if (duration < 0) duration = defaultFadeDuration;
        
        if (canvasGroup != null)
        {
            canvasGroup.DOFade(0f, duration).OnComplete(() => {
                canvasGroup.gameObject.SetActive(false);
            });
        }
    }
    
    /// <summary>
    /// Perform a complete fade in and out sequence
    /// </summary>
    public IEnumerator FadeInAndOut(float fadeInDuration = -1f, float holdDuration = 0.2f, float fadeOutDuration = -1f)
    {
        if (fadeInDuration < 0) fadeInDuration = defaultFadeDuration;
        if (fadeOutDuration < 0) fadeOutDuration = defaultFadeDuration;
        
        // Fade in
        FadeIn(fadeInDuration);
        yield return new WaitForSeconds(fadeInDuration);
        
        // Hold
        yield return new WaitForSeconds(holdDuration);
        
        // Fade out
        FadeOut(fadeOutDuration);
        yield return new WaitForSeconds(fadeOutDuration);
    }
    
    /// <summary>
    /// Check if fade screen is currently visible
    /// </summary>
    public bool IsVisible()
    {
        return canvasGroup != null && canvasGroup.gameObject.activeInHierarchy && canvasGroup.alpha > 0f;
    }
}
