using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tractor : MonoBehaviour
{
    public Transform Seed, trailer;
    public void OnTriggerEnter(Collider collision)
    {
        if (collision.gameObject.tag == "checkpoint")
        {
            collision.gameObject.GetComponent<BoxCollider>().enabled = false;
            collision.gameObject.transform.GetChild(0).gameObject.SetActive(false);
            collision.gameObject.transform.GetChild(1).gameObject.SetActive(true);
        }
        else if (collision.gameObject.tag == "finishline")
        {
            collision.gameObject.SetActive(false);
            Tructorlinkcontroll.instance.SmoothLinkDown();
        }
        else if (collision.gameObject.tag == "linkSeedM")
        {
            this.GetComponent<Rigidbody>().interpolation = RigidbodyInterpolation.None;
            collision.gameObject.SetActive(false);
         
            StartCoroutine(ResetTractorPosition());
        }
        else if (collision.gameObject.tag == "trailerlink")
        {
            this.GetComponent<Rigidbody>().interpolation = RigidbodyInterpolation.None;
            collision.gameObject.SetActive(false);
            StartCoroutine(ResetTractorPosition1());
        }

    }

    IEnumerator ResetTractorPosition()
    {
        yield return new WaitForSeconds(0.5f);
        this.transform.position = Seed.position;
        this.transform.rotation = Seed.rotation;
        yield return new WaitForSeconds(0.5f);
        this.GetComponent<Rigidbody>().interpolation = RigidbodyInterpolation.Interpolate;
    }
    IEnumerator ResetTractorPosition1()
    {
        yield return new WaitForSeconds(0.5f);
        this.transform.position = trailer.position;
        this.transform.rotation = trailer.rotation;
        yield return new WaitForSeconds(0.5f);
        this.GetComponent<Rigidbody>().interpolation = RigidbodyInterpolation.Interpolate;
    }

}
