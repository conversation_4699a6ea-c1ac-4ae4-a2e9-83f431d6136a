using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class Tructorlinkcontroll : MonoBehaviour
{
    public Button Uplink;
    public Button Downlink;
    public GameObject tractorlink;
    public  ParticleSystem[] particle;
    public float rotationSpeed = 30f;
    public float maxRotationLimit = 12f;
    public float minRotationLimit = -25f;
    private bool isUpPressed = false;
    private bool isDownPressed = false;
    private RCC_CarControllerV3 carController;
    public static Tructorlinkcontroll instance;
    private Coroutine smoothMoveCoroutine;

    void Awake()
    {
        instance = this;
    }

    void Start()
    {
        // Add button press/release events
        AddButtonEvents(Uplink, () => isUpPressed = true, () => isUpPressed = false);
        AddButtonEvents(Downlink, () => isDownPressed = true, () => isDownPressed = false);

        // Find the RCC car controller
        carController = FindObjectOfType<RCC_CarControllerV3>();
    }

    void Update()
    {
        if (tractorlink != null)
        {
            Vector3 currentRotation = tractorlink.transform.localEulerAngles;
            float currentX = currentRotation.x > 180 ? currentRotation.x - 360 : currentRotation.x;

            if (isUpPressed && currentX < maxRotationLimit)
            {
                currentX += rotationSpeed * Time.deltaTime;
                currentX = Mathf.Clamp(currentX, minRotationLimit, maxRotationLimit);
                tractorlink.transform.localEulerAngles = new Vector3(currentX, currentRotation.y, currentRotation.z);
            }
            else if (isDownPressed && currentX > minRotationLimit)
            {
                currentX -= rotationSpeed * Time.deltaTime;
                currentX = Mathf.Clamp(currentX, minRotationLimit, maxRotationLimit);
                tractorlink.transform.localEulerAngles = new Vector3(currentX, currentRotation.y, currentRotation.z);
            }

            // Control particle systems based on rotation and speed
            if (particle != null && particle.Length > 0)
            {
                // Check if speed is greater than 5 and rotation is within limits
                bool speedCondition = carController != null && carController.speed > 5f;
                bool rotationCondition = currentX >= -25f && currentX <= -8f;

                if (speedCondition && rotationCondition)
                {
                    // Play all particle systems in the array
                    foreach (ParticleSystem ps in particle)
                    {
                        if (ps != null && !ps.isPlaying)
                            ps.Play();
                    }
                }
                else
                {
                    // Stop all particle systems in the array
                    foreach (ParticleSystem ps in particle)
                    {
                        if (ps != null && ps.isPlaying)
                            ps.Stop();
                    }
                }
            }
        }
    }

    void AddButtonEvents(Button button, System.Action onPress, System.Action onRelease)
    {
        EventTrigger trigger = button.gameObject.GetComponent<EventTrigger>();
        if (trigger == null) trigger = button.gameObject.AddComponent<EventTrigger>();

        EventTrigger.Entry pressEntry = new EventTrigger.Entry();
        pressEntry.eventID = EventTriggerType.PointerDown;
        pressEntry.callback.AddListener((data) => onPress());
        trigger.triggers.Add(pressEntry);

        EventTrigger.Entry releaseEntry = new EventTrigger.Entry();
        releaseEntry.eventID = EventTriggerType.PointerUp;
        releaseEntry.callback.AddListener((data) => onRelease());
        trigger.triggers.Add(releaseEntry);
    }

    // Method to smoothly move link down to -25 degrees
    public void SmoothLinkDown()
    {
        if (smoothMoveCoroutine != null)
        {
            StopCoroutine(smoothMoveCoroutine);
        }
        smoothMoveCoroutine = StartCoroutine(SmoothMoveToPosition(-25f));
    }

    private System.Collections.IEnumerator SmoothMoveToPosition(float targetRotation)
    {
        if (tractorlink == null) yield break;

        Vector3 currentRotation = tractorlink.transform.localEulerAngles;
        float currentX = currentRotation.x > 180 ? currentRotation.x - 360 : currentRotation.x;
        float startX = currentX;

        float duration = 2f; // 2 seconds for smooth movement
        float elapsedTime = 0f;

        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / duration;

            // Use smooth step for easing
            t = Mathf.SmoothStep(0f, 1f, t);

            float newX = Mathf.Lerp(startX, targetRotation, t);
            newX = Mathf.Clamp(newX, minRotationLimit, maxRotationLimit);

            tractorlink.transform.localEulerAngles = new Vector3(newX, currentRotation.y, currentRotation.z);

            yield return null;
        }

        // Ensure we end exactly at target rotation
        tractorlink.transform.localEulerAngles = new Vector3(targetRotation, currentRotation.y, currentRotation.z);
        smoothMoveCoroutine = null;
    }
}
